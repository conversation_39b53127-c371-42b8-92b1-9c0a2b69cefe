import { axiosInstance } from "@/lib/axios";

export interface AuthUser {
  id: string;
  username: string;
  email: string;
  isVerified: boolean;
  xp: number;
  level: number;
  profilePicture?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  success: boolean;
  data: AuthUser;
  message: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface SignupData {
  username: string;
  email: string;
  password: string;
}

export interface ForgotPasswordData {
  email: string;
}

// Auth API functions
export const authAPI = {
  // Check authentication status
  checkAuth: async (): Promise<AuthUser> => {
    try {
      const response = await axiosInstance.get<AuthResponse>("/auth/check");
      return response.data.data;
    } catch (error: any) {
      if (error.response?.status === 401) {
        // Try to refresh token
        try {
          await axiosInstance.get("/refresh-token");
          const response = await axiosInstance.get<AuthResponse>("/auth/check");
          return response.data.data;
        } catch (refreshError) {
          throw new Error("Authentication failed");
        }
      }
      throw error;
    }
  },

  // Login user
  login: async (data: LoginData): Promise<AuthUser> => {
    const response = await axiosInstance.post<AuthResponse>("/auth/login", data);
    localStorage.setItem("userInfo", JSON.stringify(response.data.data));
    return response.data.data;
  },

  // Register user
  signup: async (data: SignupData): Promise<AuthResponse> => {
    const response = await axiosInstance.post<AuthResponse>("/auth/register", data);
    return response.data;
  },

  // Forgot password
  forgotPassword: async (data: ForgotPasswordData): Promise<AuthResponse> => {
    const response = await axiosInstance.post<AuthResponse>("/auth/forgot-password", data);
    return response.data;
  },

  // Logout user
  logout: async (): Promise<void> => {
    await axiosInstance.post("/auth/logout");
    localStorage.removeItem("userInfo");
  },
};
