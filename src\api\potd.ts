import { axiosInstance } from "@/lib/axios";
import { Problem } from "./problems";

export interface Potd {
  id: string;
  date: Date;
  problemId: string;
  userId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  problem: Problem;
  solvedUsers: string[];
}

export interface PotdResponse {
  success: boolean;
  data: Potd;
  message: string;
}

// POTD API functions
export const potdAPI = {
  // Get problem of the day
  getPotd: async (): Promise<Potd> => {
    const response = await axiosInstance.get<PotdResponse>("/potd/get-potd");
    return response.data.data;
  },
};
