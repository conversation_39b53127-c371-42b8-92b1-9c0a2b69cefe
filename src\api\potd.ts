import { axiosInstance } from "@/lib/axios";
import { toast } from "sonner";
import { Problem } from "./problems";

export interface Potd {
  id: string;
  date: Date;
  problemId: string;
  userId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  problem: Problem;
  solvedUsers: string[];
}

export interface PotdResponse {
  success: boolean;
  data: Potd;
  message: string;
}

// POTD API functions
export const potdAPI = {
  // Get problem of the day
  getPotd: async (): Promise<Potd> => {
    try {
      const response = await axiosInstance.get<PotdResponse>("/potd/get-potd");
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching POTD:", error);
      toast.error("Failed to fetch problem of the day");
      throw error;
    }
  },
};
