import { axiosInstance } from "@/lib/axios";

export interface Problem {
  id: string;
  title: string;
  description: string;
  difficulty: "Easy" | "Medium" | "Hard";
  tags: string[];
  constraints: string;
  examples: Array<{
    input: string;
    output: string;
    explanation?: string;
  }>;
  testCases: Array<{
    input: string;
    expectedOutput: string;
  }>;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  solvedBy?: string[];
  submissions?: number;
  acceptanceRate?: number;
}

export interface ProblemsResponse {
  success: boolean;
  data: {
    problems: Problem[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalProblems: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
  message: string;
}

export interface ProblemResponse {
  success: boolean;
  data: Problem;
  message: string;
}

export interface FilterQuery {
  search?: string;
  difficulty?: string;
  tags?: string;
  filter?: string;
}

// Problems API functions
export const problemsAPI = {
  // Get all problems with pagination
  getAllProblems: async (page: number = 1): Promise<ProblemsResponse["data"]> => {
    const response = await axiosInstance.get<ProblemsResponse>(
      `/problems/get-all-problems?page=${page}`
    );
    return response.data.data;
  },

  // Get problem by ID
  getProblemById: async (id: string): Promise<Problem> => {
    const response = await axiosInstance.get<ProblemResponse>(`/problems/get-problem/${id}`);
    return response.data.data;
  },

  // Get top 3 most solved problems
  getTop3Problems: async (): Promise<Problem[]> => {
    const response = await axiosInstance.get<{ success: boolean; data: Problem[] }>(
      "/problems/get-most-solved-3problem"
    );
    return response.data.data;
  },

  // Get solved problems by user
  getSolvedProblemsByUser: async (): Promise<Problem[]> => {
    const response = await axiosInstance.get<{ success: boolean; data: Problem[] }>(
      "/problems/get-solved-problems"
    );
    return response.data.data;
  },

  // Get problems created by user
  getProblemsCreatedByUser: async (): Promise<Problem[]> => {
    const response = await axiosInstance.get<{ success: boolean; data: Problem[] }>(
      "/problems/problem-created-by-user"
    );
    return response.data.data;
  },

  // Get all tags
  getAllTags: async (): Promise<string[]> => {
    const response = await axiosInstance.get<{ success: boolean; data: string[] }>(
      "/problems/get-all-tags"
    );
    return response.data.data;
  },

  // Get all companies challenges
  getAllCompaniesChallenges: async (): Promise<any[]> => {
    const response = await axiosInstance.get<{ success: boolean; data: any[] }>(
      "/problems/get-all-companies-challenges"
    );
    return response.data.data;
  },

  // Get random problem
  getRandomProblem: async (): Promise<Problem> => {
    const response = await axiosInstance.get<ProblemResponse>("problems/random-problem");
    return response.data.data;
  },

  // Create problem
  createProblem: async (problem: FormData): Promise<any> => {
    const response = await axiosInstance.post("/problems/create-problem", problem);
    return response.data;
  },
};
