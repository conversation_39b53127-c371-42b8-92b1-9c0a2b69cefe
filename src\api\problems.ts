import { axiosInstance } from "@/lib/axios";
import { toast } from "sonner";

export interface Problem {
  id: string;
  title: string;
  description: string;
  difficulty: "Easy" | "Medium" | "Hard";
  tags: string[];
  constraints: string;
  examples: Array<{
    input: string;
    output: string;
    explanation?: string;
  }>;
  testCases: Array<{
    input: string;
    expectedOutput: string;
  }>;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  solvedBy?: string[];
  submissions?: number;
  acceptanceRate?: number;
}

export interface ProblemsResponse {
  success: boolean;
  data: {
    problems: Problem[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalProblems: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
      message: string;
    };
  };
  message: string;
}

export interface ProblemResponse {
  success: boolean;
  data: Problem;
  message: string;
}

// Pure API functions - no state storage
export const problemsAPI = {
  // Get all problems with pagination
  getAllProblems: async (page: number = 1): Promise<ProblemsResponse["data"]> => {
    try {
      const response = await axiosInstance.get<ProblemsResponse>(
        `/problems/get-all-problems?page=${page}`
      );
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching problems:", error);
      toast.error("Failed to fetch problems");
      throw error;
    }
  },

  // Get problem by ID
  getProblemById: async (id: string): Promise<Problem> => {
    try {
      const response = await axiosInstance.get<ProblemResponse>(`/problems/get-problem/${id}`);
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching problem:", error);
      toast.error("Failed to fetch problem");
      throw error;
    }
  },

  // Get top 3 most solved problems
  getTop3Problems: async (): Promise<Problem[]> => {
    try {
      const response = await axiosInstance.get<{ success: boolean; data: Problem[] }>(
        "/problems/get-most-solved-3problem"
      );
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching top 3 problems:", error);
      toast.error("Failed to fetch top problems");
      throw error;
    }
  },

  // Get solved problems by user
  getSolvedProblemsByUser: async (): Promise<Problem[]> => {
    try {
      const response = await axiosInstance.get<{ success: boolean; data: Problem[] }>(
        "/problems/get-solved-problems"
      );
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching solved problems:", error);
      toast.error("Failed to fetch solved problems");
      throw error;
    }
  },

  // Get problems created by user
  getProblemsCreatedByUser: async (): Promise<Problem[]> => {
    try {
      const response = await axiosInstance.get<{ success: boolean; data: Problem[] }>(
        "/problems/problem-created-by-user"
      );
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching created problems:", error);
      toast.error("Failed to fetch created problems");
      throw error;
    }
  },

  // Get all tags
  getAllTags: async (): Promise<string[]> => {
    try {
      const response = await axiosInstance.get<{ success: boolean; data: string[] }>(
        "/problems/get-all-tags"
      );
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching tags:", error);
      toast.error("Failed to fetch tags");
      throw error;
    }
  },

  // Get all companies challenges
  getAllCompaniesChallenges: async (): Promise<any[]> => {
    try {
      const response = await axiosInstance.get<{ success: boolean; data: any[] }>(
        "/problems/get-all-companies-challenges"
      );
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching companies challenges:", error);
      toast.error("Failed to fetch companies challenges");
      throw error;
    }
  },

  // Get random problem
  getRandomProblem: async (): Promise<Problem> => {
    try {
      const response = await axiosInstance.get<ProblemResponse>("problems/random-problem");
      return response.data.data;
    } catch (error: any) {
      console.error("Error fetching random problem:", error);
      toast.error("Failed to fetch random problem");
      throw error;
    }
  },

  // Create problem
  createProblem: async (problem: FormData): Promise<any> => {
    try {
      const response = await axiosInstance.post("/problems/create-problem", problem);
      toast.success("Problem created successfully!");
      return response.data;
    } catch (error: any) {
      console.error("Error creating problem:", error);
      toast.error("Failed to create problem");
      throw error;
    }
  },
};
