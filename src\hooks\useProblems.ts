import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
} from "@tanstack/react-query";
import { toast } from "sonner";
import { useProblemStore } from "@/store/useProblemStore";

// Query keys
export const problemKeys = {
  all: ["problems"] as const,
  lists: () => [...problemKeys.all, "list"] as const,
  list: (page: number) => [...problemKeys.lists(), page] as const,
  details: () => [...problemKeys.all, "detail"] as const,
  detail: (id: string) => [...problemKeys.details(), id] as const,
  top3: () => [...problemKeys.all, "top3"] as const,
  solved: () => [...problemKeys.all, "solved"] as const,
  created: () => [...problemKeys.all, "created"] as const,
  tags: () => [...problemKeys.all, "tags"] as const,
  companies: () => [...problemKeys.all, "companies"] as const,
  random: () => [...problemKeys.all, "random"] as const,
};

// Get all problems with pagination
export const useProblems = (page: number = 1) => {
  const getAllProblems = useProblemStore((state) => state.getAllProblems);
  const problems = useProblemStore((state) => state.problems);

  return useQuery({
    queryKey: problemKeys.list(page),
    queryFn: async () => {
      await getAllProblems(page);
      return useProblemStore.getState().problems;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    initialData: problems,
  });
};

// Get problems with infinite scroll
export const useInfiniteProblems = () => {
  const getAllProblems = useProblemStore((state) => state.getAllProblems);

  return useInfiniteQuery({
    queryKey: problemKeys.lists(),
    queryFn: async ({ pageParam = 1 }) => {
      await getAllProblems(pageParam);
      const state = useProblemStore.getState();
      return state.problems;
    },
    getNextPageParam: (lastPage) => {
      return lastPage?.pagination?.hasNextPage
        ? lastPage.pagination.currentPage + 1
        : undefined;
    },
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Get problem by ID
export const useProblem = (id: string) => {
  const getProblemById = useProblemStore((state) => state.getProblemById);
  const problem = useProblemStore((state) => state.problem);

  return useQuery({
    queryKey: problemKeys.detail(id),
    queryFn: async () => {
      await getProblemById(id);
      return useProblemStore.getState().problem;
    },
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    initialData: problem,
  });
};

// Get top 3 problems
export const useTop3Problems = () => {
  const getTop3Problems = useProblemStore((state) => state.getTop3Problems);
  const top3Problems = useProblemStore((state) => state.top3Problems);

  return useQuery({
    queryKey: problemKeys.top3(),
    queryFn: async () => {
      await getTop3Problems();
      return useProblemStore.getState().top3Problems;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    initialData: top3Problems,
  });
};

// Get solved problems by user
export const useSolvedProblems = () => {
  const getSolvedProblemByUser = useProblemStore(
    (state) => state.getSolvedProblemByUser
  );
  const solvedProblems = useProblemStore((state) => state.solvedProblems);

  return useQuery({
    queryKey: problemKeys.solved(),
    queryFn: async () => {
      await getSolvedProblemByUser();
      return useProblemStore.getState().solvedProblems;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    initialData: solvedProblems,
  });
};

// Get problems created by user
export const useCreatedProblems = () => {
  const getAllProblemCreatedByUser = useProblemStore(
    (state) => state.getAllProblemCreatedByUser
  );
  const createdByUserProblems = useProblemStore(
    (state) => state.createdByUserProblems
  );

  return useQuery({
    queryKey: problemKeys.created(),
    queryFn: async () => {
      await getAllProblemCreatedByUser();
      return useProblemStore.getState().createdByUserProblems;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    initialData: createdByUserProblems,
  });
};

// Get all tags
export const useTags = () => {
  const getAllTags = useProblemStore((state) => state.getAllTags);
  const tags = useProblemStore((state) => state.tags);

  return useQuery({
    queryKey: problemKeys.tags(),
    queryFn: async () => {
      await getAllTags();
      return useProblemStore.getState().tags;
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    initialData: tags,
  });
};

// Get companies challenges
export const useCompaniesChallenges = () => {
  const getAllCompaniesChallenges = useProblemStore(
    (state) => state.getAllCompaniesChallenges
  );
  const companiesChallenges = useProblemStore(
    (state) => state.companiesChallenges
  );

  return useQuery({
    queryKey: problemKeys.companies(),
    queryFn: async () => {
      await getAllCompaniesChallenges();
      return useProblemStore.getState().companiesChallenges;
    },
    staleTime: 30 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
    initialData: companiesChallenges,
  });
};

// Get random problem
export const useRandomProblem = () => {
  const getRandomProblem = useProblemStore((state) => state.getRandomProblem);
  const randomProblem = useProblemStore((state) => state.randomProblem);

  return useQuery({
    queryKey: problemKeys.random(),
    queryFn: async () => {
      await getRandomProblem();
      return useProblemStore.getState().randomProblem;
    },
    enabled: false, // Only fetch when explicitly called
    staleTime: 0, // Always fresh
    gcTime: 5 * 60 * 1000,
    initialData: randomProblem,
  });
};

// Create problem mutation
export const useCreateProblem = () => {
  const queryClient = useQueryClient();
  const createProblem = useProblemStore((state) => state.createProblem);

  return useMutation({
    mutationFn: createProblem,
    onSuccess: (data) => {
      // Success handling is already done in the Zustand store
      // Invalidate problems list to refetch
      queryClient.invalidateQueries({ queryKey: problemKeys.lists() });
      queryClient.invalidateQueries({ queryKey: problemKeys.created() });
    },
    onError: (error: any) => {
      // Error handling is already done in the Zustand store
      console.error("Create problem error:", error);
    },
  });
};
