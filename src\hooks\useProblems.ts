import { useQuery } from "@tanstack/react-query";
import { useProblemStore } from "@/store/useProblemStore";

// Query keys
export const problemKeys = {
  all: ["problems"] as const,
  lists: () => [...problemKeys.all, "list"] as const,
  list: (page: number) => [...problemKeys.lists(), page] as const,
};

// Get all problems with pagination using React Query
export const useProblems = (page: number = 1) => {
  const getAllProblems = useProblemStore((state) => state.getAllProblems);
  const problems = useProblemStore((state) => state.problems);

  return useQuery({
    queryKey: problemKeys.list(page),
    queryFn: async () => {
      await getAllProblems(page);
      return useProblemStore.getState().problems;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    initialData: problems,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });
};

// Get random problem - for Dashboard
export const useRandomProblem = () => {
  const getRandomProblem = useProblemStore((state) => state.getRandomProblem);
  const randomProblem = useProblemStore((state) => state.randomProblem);

  return useQuery({
    queryKey: [...problemKeys.all, "random"],
    queryFn: async () => {
      await getRandomProblem();
      return useProblemStore.getState().randomProblem;
    },
    enabled: false, // Only fetch when explicitly called
    staleTime: 0, // Always fresh
    gcTime: 5 * 60 * 1000,
    initialData: randomProblem,
  });
};

// Get top 3 problems - for Index page
export const useTop3Problems = () => {
  const getTop3Problems = useProblemStore((state) => state.getTop3Problems);
  const top3Problems = useProblemStore((state) => state.top3Problems);

  return useQuery({
    queryKey: [...problemKeys.all, "top3"],
    queryFn: async () => {
      await getTop3Problems();
      return useProblemStore.getState().top3Problems;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    initialData: top3Problems,
  });
};

// Get all tags - for Index page
export const useTags = () => {
  const getAllTags = useProblemStore((state) => state.getAllTags);
  const tags = useProblemStore((state) => state.tags);

  return useQuery({
    queryKey: [...problemKeys.all, "tags"],
    queryFn: async () => {
      await getAllTags();
      return useProblemStore.getState().tags;
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    initialData: tags,
  });
};

// Get companies challenges - for Index page
export const useCompaniesChallenges = () => {
  const getAllCompaniesChallenges = useProblemStore(
    (state) => state.getAllCompaniesChallenges
  );
  const companiesChallenges = useProblemStore(
    (state) => state.companiesChallenges
  );

  return useQuery({
    queryKey: [...problemKeys.all, "companies"],
    queryFn: async () => {
      await getAllCompaniesChallenges();
      return useProblemStore.getState().companiesChallenges;
    },
    staleTime: 30 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
    initialData: companiesChallenges,
  });
};
