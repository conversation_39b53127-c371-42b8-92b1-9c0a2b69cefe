import { useQuery } from "@tanstack/react-query";
import { useProblemStore } from "@/store/useProblemStore";

// Query keys
export const problemKeys = {
  all: ["problems"] as const,
  lists: () => [...problemKeys.all, "list"] as const,
  list: (page: number) => [...problemKeys.lists(), page] as const,
};

// Get all problems with pagination using React Query
export const useProblems = (page: number = 1) => {
  const getAllProblems = useProblemStore((state) => state.getAllProblems);
  const problems = useProblemStore((state) => state.problems);

  return useQuery({
    queryKey: problemKeys.list(page),
    queryFn: async () => {
      await getAllProblems(page);
      return useProblemStore.getState().problems;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    initialData: problems,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });
};
