import { Outlet } from "react-router-dom";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuthCheck } from "@/hooks/useAuth";
import { useProblems } from "@/hooks/useProblems";

function AuthCheck() {
  const navigate = useNavigate();
  const { data: authUser, isLoading: isCheckingAuth, error } = useAuthCheck();
  const userInfo = localStorage.getItem("userInfo");

  // Prefetch problems when user is authenticated
  useProblems(1);

  useEffect(() => {
    // If auth check fails and no local user info, redirect to signin
    if (error && !userInfo && !authUser) {
      navigate("/signin");
    }
  }, [error, userInfo, authUser, navigate]);

  if (isCheckingAuth) {
    return (
      <div className="min-h-screen bg-craft-bg flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  // If no auth user and no local storage, redirect
  if (!authUser && !userInfo) {
    navigate("/signin");
    return null;
  }

  return (
    <>
      <Outlet />
    </>
  );
}

export default AuthCheck;
