import { useQuery } from "@tanstack/react-query";
import { usePotdStore } from "@/store/usePotdStore";

// Query keys
export const potdKeys = {
  all: ["potd"] as const,
  current: () => [...potdKeys.all, "current"] as const,
};

// Get problem of the day
export const usePotd = () => {
  const getPotd = usePotdStore((state) => state.getPotd);
  const potd = usePotdStore((state) => state.potd);

  return useQuery({
    queryKey: potdKeys.current(),
    queryFn: async () => {
      await getPotd();
      return usePotdStore.getState().potd;
    },
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
    retry: 2,
    initialData: potd,
  });
};
