import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import {
  authAPI,
  type AuthUser,
  type LoginData,
  type SignupData,
  type ForgotPasswordData,
} from "@/api/auth";

// Query keys
export const authKeys = {
  all: ["auth"] as const,
  user: () => [...authKeys.all, "user"] as const,
};

// Check authentication hook
export const useAuthCheck = () => {
  return useQuery({
    queryKey: authKeys.user(),
    queryFn: authAPI.checkAuth,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Login mutation hook
export const useLogin = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation<AuthUser, any, LoginData>({
    mutationFn: authAPI.login,
    onSuccess: (data: AuthUser) => {
      // Update the auth query cache
      queryClient.setQueryData(authKeys.user(), data);
      toast.success("Login successful!");
      navigate("/dashboard");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Login failed");
    },
  });
};

// Signup mutation hook
export const useSignup = () => {
  const navigate = useNavigate();

  return useMutation<any, any, SignupData>({
    mutationFn: authAPI.signup,
    onSuccess: (data) => {
      if (data.success) {
        toast.success(
          "Account created successfully! Please verify your email."
        );
        navigate("/verify-account");
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Signup failed");
    },
  });
};

// Forgot password mutation hook
export const useForgotPassword = () => {
  return useMutation<any, any, ForgotPasswordData>({
    mutationFn: authAPI.forgotPassword,
    onSuccess: (data) => {
      if (data.success) {
        toast.success("Check your email for password reset instructions");
      }
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to send reset email"
      );
    },
  });
};

// Logout mutation hook
export const useLogout = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: authAPI.logout,
    onSuccess: () => {
      // Clear all queries
      queryClient.clear();
      toast.success("Logged out successfully");
      navigate("/signin");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Logout failed");
    },
  });
};

// Get current user from cache
export const useCurrentUser = () => {
  const queryClient = useQueryClient();
  return queryClient.getQueryData<AuthUser>(authKeys.user());
};
