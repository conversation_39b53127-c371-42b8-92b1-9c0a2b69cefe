import { useQuery } from "@tanstack/react-query";
import { useAuthStore } from "@/store/useAuthStore";

// Query keys
export const authKeys = {
  all: ["auth"] as const,
  user: () => [...authKeys.all, "user"] as const,
};

// Check authentication hook using React Query
export const useAuthCheck = () => {
  const checkAuth = useAuthStore((state) => state.checkAuth);
  const authUser = useAuthStore((state) => state.authUser);

  return useQuery({
    queryKey: authKeys.user(),
    queryFn: async () => {
      await checkAuth();
      return useAuthStore.getState().authUser;
    },
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    initialData: authUser,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });
};

// Get current user from Zustand store
export const useCurrentUser = () => {
  const authUser = useAuthStore((state) => state.authUser);
  return authUser;
};
