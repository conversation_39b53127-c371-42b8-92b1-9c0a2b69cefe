import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useAuthStore } from "@/store/useAuthStore";

// Query keys
export const authKeys = {
  all: ["auth"] as const,
  user: () => [...authKeys.all, "user"] as const,
};

// Check authentication hook
export const useAuthCheck = () => {
  const checkAuth = useAuthStore((state) => state.checkAuth);
  const authUser = useAuthStore((state) => state.authUser);

  return useQuery({
    queryKey: authKeys.user(),
    queryFn: async () => {
      await checkAuth();
      return useAuthStore.getState().authUser;
    },
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    initialData: authUser,
  });
};

// Login mutation hook
export const useLogin = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const signin = useAuthStore((state) => state.signin);

  return useMutation({
    mutationFn: signin,
    onSuccess: (data) => {
      if (data?.data?.data) {
        // Update the auth query cache
        queryClient.setQueryData(authKeys.user(), data.data.data);
        toast.success("Login successful!");
        navigate("/dashboard");
      }
    },
    onError: (error: any) => {
      // Error handling is already done in the Zustand store
      console.error("Login error:", error);
    },
  });
};

// Signup mutation hook
export const useSignup = () => {
  const navigate = useNavigate();
  const signup = useAuthStore((state) => state.signup);

  return useMutation({
    mutationFn: signup,
    onSuccess: (data) => {
      if (data?.data?.success) {
        toast.success(
          "Account created successfully! Please verify your email."
        );
        navigate("/verify-account");
      }
    },
    onError: (error: any) => {
      // Error handling is already done in the Zustand store
      console.error("Signup error:", error);
    },
  });
};

// Forgot password mutation hook
export const useForgotPassword = () => {
  const forgotPassword = useAuthStore((state) => state.forgotPassword);

  return useMutation({
    mutationFn: forgotPassword,
    onSuccess: (data) => {
      // Success handling is already done in the Zustand store
    },
    onError: (error: any) => {
      // Error handling is already done in the Zustand store
      console.error("Forgot password error:", error);
    },
  });
};

// Logout mutation hook
export const useLogout = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const logout = useAuthStore((state) => state.logout);

  return useMutation({
    mutationFn: logout,
    onSuccess: () => {
      // Clear all queries
      queryClient.clear();
      toast.success("Logged out successfully");
      navigate("/signin");
    },
    onError: (error: any) => {
      // Error handling is already done in the Zustand store
      console.error("Logout error:", error);
    },
  });
};

// Get current user from cache or Zustand store
export const useCurrentUser = () => {
  const authUser = useAuthStore((state) => state.authUser);
  return authUser;
};
